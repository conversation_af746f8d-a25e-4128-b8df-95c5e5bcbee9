// Test to verify submission value matching for select fields
// Based on the actual submission data provided

// Mock field definitions that might correspond to the submission data
const mockFields = [
  {
    field_name: "immediate_safety_concerns",
    field_type: "select_field",
    options: [
      { option_key: "none", option_value: "None" },
      { option_key: "minor", option_value: "Minor" },
      { option_key: "moderate", option_value: "Moderate" },
      { option_key: "great", option_value: "Great" },
      { option_key: "severe", option_value: "Severe" }
    ]
  },
  {
    field_name: "risk_level", 
    field_type: "select_field",
    options: [
      { option_key: "low", option_value: "Low" },
      { option_key: "medium", option_value: "Medium" },
      { option_key: "high", option_value: "High" },
      { option_key: "critical", option_value: "Critical" }
    ]
  },
  {
    field_name: "specific_vulnerabilities",
    field_type: "select_field", 
    options: [
      { option_key: "true", option_value: "Yes" },
      { option_key: "false", option_value: "No" }
    ]
  }
];

// Mock submission data from the API response
const submissionData = {
  "immediate_safety_concerns": "great",
  "risk_level": "high", 
  "specific_vulnerabilities": "true",
  "protection_incidents": ""
};

// Mock the matching function
function matchDefaultValueToOption(defaultValue, options) {
  if (!defaultValue || !options || options.length === 0) {
    return "";
  }

  const defaultStr = String(defaultValue);

  // First try exact match with option_key (value)
  const exactValueMatch = options.find(opt => opt.option_key === defaultStr);
  if (exactValueMatch) {
    return exactValueMatch.option_key;
  }

  // Then try exact match with option_value (label)
  const exactLabelMatch = options.find(opt => opt.option_value === defaultStr);
  if (exactLabelMatch) {
    return exactLabelMatch.option_key;
  }

  // Try case-insensitive match with option_value (label)
  const caseInsensitiveLabelMatch = options.find(opt => 
    opt.option_value.toLowerCase() === defaultStr.toLowerCase()
  );
  if (caseInsensitiveLabelMatch) {
    return caseInsensitiveLabelMatch.option_key;
  }

  return "";
}

console.log("Testing submission value matching...\n");

// Test each field from the submission
mockFields.forEach(field => {
  const submittedValue = submissionData[field.field_name];
  if (submittedValue !== undefined && submittedValue !== null && submittedValue !== "") {
    const matchedValue = matchDefaultValueToOption(submittedValue, field.options);
    
    console.log(`Field: ${field.field_name}`);
    console.log(`  Submitted: "${submittedValue}"`);
    console.log(`  Matched: "${matchedValue}"`);
    console.log(`  Options: ${field.options.map(opt => `${opt.option_key}="${opt.option_value}"`).join(', ')}`);
    console.log(`  Status: ${matchedValue ? '✓ MATCHED' : '✗ NO MATCH'}\n`);
  }
});

console.log("Test completed!");
