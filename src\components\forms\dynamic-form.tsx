"use client";

import React, { useEffect, useCallback, useRef, useMemo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { Loader2 } from "lucide-react";

import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";

import { useFormFields } from "@/app/[locale]/_modules/cm/hooks/useFormFields";
import { FormField, FormSubmission } from "@/types/cm";
import { TinputField } from "@/types";

import {
  convertNewAPIFormFieldToInputField,
  getFieldDefaultValue,
  matchDefaultValueToOption,
} from "@/utils/form-field-helpers";
import { createDynamicFormValidationSchema } from "@/utils/dynamic-form-validation";

type DynamicFormProps = {
  formSectionId: string | number;
  caseId?: string | number;
  submission?: FormSubmission[];
  shouldGoNextRef: React.MutableRefObject<boolean>;
  submissionsLoading: boolean;
  onSubmit: (
    data: Record<string, unknown>,
  ) => Promise<{ success: boolean; newCaseId: string | null }>;
  onSaveDraft?: (data: Record<string, unknown>) => Promise<void>;
  onNext?: () => void;
  onPrevious?: () => void;
  isSubmitting?: boolean;
  isDraftSaving?: boolean;
  mode?: "create" | "edit";
  canGoNext?: boolean;
  canGoPrevious?: boolean;
  isFirstSection?: boolean;
  isLastSection?: boolean;
  currentSectionIndex?: number;
  totalSections?: number;
};

// Convert backend field format to frontend input field
const convertFormFieldToInputField = (
  field: FormField,
): TinputField<Record<string, unknown>> | null => {
  return convertNewAPIFormFieldToInputField(field);
};

export const DynamicForm = ({
  formSectionId,
  caseId,
  submission,
  shouldGoNextRef,
  onSubmit,
  onNext,
  onPrevious,
  isSubmitting = false,
  isDraftSaving = false,
  mode = "create",
  canGoNext = false,
  canGoPrevious = false,
  isFirstSection = false,
  isLastSection = false,
  currentSectionIndex = 0,
  totalSections = 1,
}: DynamicFormProps) => {
  const t = useTranslations();

  const {
    fields = [],
    isLoading,
    error,
  } = useFormFields({
    formSectionId,
    ...(mode === "edit" ? { caseId } : {}),
  });

  // Create validation schema based on fields
  const validationSchema = useMemo(() => {
    if (!fields || fields.length === 0) return null;
    return createDynamicFormValidationSchema(fields, t);
  }, [fields, t]);

  const getDefaultValues = useCallback(
    (
      formFields: FormField[],
      submission?: FormSubmission[],
    ): Record<string, unknown> => {
      const defaults: Record<string, unknown> = {};

      // Try to populate from submission if editing
      const submittedData =
        mode === "edit" && submission?.length
          ? (submission.find(
              (sub) =>
                sub.attributes.form_section_id?.toString() ===
                formSectionId.toString(),
            )?.attributes.form_data ?? {})
          : {};

      formFields.forEach((field) => {
        const name = field.field_name;
        const submittedValue = submittedData[name];
        console.log("🚀 ~ DynamicForm ~ name:", name);
        console.log("🚀 ~ DynamicForm ~ submittedValue:", submittedValue);

        // If a value exists in submission, use it
        if (submittedValue !== undefined && submittedValue !== null) {
          // For select fields, ensure the submitted value matches a valid option
          if (
            (field.field_type === "select_field" ||
              field.field_type === "select") &&
            field.options
          ) {
            const matchedValue = matchDefaultValueToOption(
              submittedValue,
              field.options,
            );
            defaults[name] = matchedValue;
            console.log("🚀 ~ Submission value matched for select:", {
              fieldName: name,
              submittedValue,
              matchedValue,
              options: field.options?.map((opt) => ({
                key: opt.option_key,
                value: opt.option_value,
              })),
            });
          } else {
            defaults[name] = submittedValue;
          }
        } else {
          // Use the new function that checks for configured default values
          const defaultValue = getFieldDefaultValue(field);
          defaults[name] = defaultValue;

          // Debug logging for select fields
          if (
            field.field_type === "select_field" ||
            field.field_type === "select"
          ) {
            console.log("🚀 ~ Select field debug:", {
              fieldName: name,
              fieldType: field.field_type,
              defaultValue,
              fieldConfig: field.field_config,
              explicitDefault: field.default_value,
              options: field.options?.map((opt) => ({
                key: opt.option_key,
                value: opt.option_value,
              })),
            });
          }
        }
      });
      console.log("🚀 ~ DynamicForm ~ Final defaults:", defaults);
      console.log("🚀 ~ DynamicForm ~ Submission data used:", submittedData);
      console.log("🚀 ~ DynamicForm ~ Mode:", mode);
      console.log("🚀 ~ DynamicForm ~ Form section ID:", formSectionId);

      return defaults;
    },
    [mode, formSectionId],
  );

  // Calculate initial default values immediately
  const initialDefaultValues = useMemo(() => {
    if (!fields || fields.length === 0) {
      return {}; // Return empty object if no fields yet
    }
    return getDefaultValues(fields, submission);
  }, [fields, submission, getDefaultValues]);

  const form = useForm({
    resolver: validationSchema ? zodResolver(validationSchema) : undefined,
    defaultValues: initialDefaultValues, // ✅ Use calculated defaults
    mode: "onChange",
  });

  const lastResetKey = useRef<string>("");

  // Only reset if the form section changes, not on every render
  useEffect(() => {
    if (!isLoading && fields && fields.length > 0) {
      const resetKey = `${formSectionId}-${fields.length}-${mode}`;

      if (lastResetKey.current !== resetKey) {
        const values = getDefaultValues(fields, submission);
        form.reset(values);
        lastResetKey.current = resetKey;
      }
    }
  }, [formSectionId, fields, submission, mode]); // ✅ Reduced dependencies

  if (error) {
    console.error("Form fields loading error:", error);
    return (
      <div className="p-4 text-red-600 bg-red-50 rounded-lg">
        <h3 className="font-medium mb-2">خطأ في تحميل حقول النموذج</h3>
        <p className="text-sm">
          فشل في تحميل حقول النموذج. يرجى المحاولة مرة أخرى.
        </p>
        <details className="mt-2 text-xs">
          <summary className="cursor-pointer">تفاصيل الخطأ</summary>
          <pre className="mt-1 whitespace-pre-wrap">{error.message}</pre>
        </details>
      </div>
    );
  }

  const handleSubmit = async (data: Record<string, any>) => {
    const result = await onSubmit(data);
    if (result.success && shouldGoNextRef.current && !isLastSection && onNext) {
      onNext();
      shouldGoNextRef.current = false;
    }
  };

  const handleNext = () => {
    form.handleSubmit(handleSubmit)();
  };

  const handlePrevious = () => {
    onPrevious?.();
  };

  const validFields = fields.filter(
    (field) => field?.field_name?.trim() !== "",
  );

  console.log("🚀 ~ validFields:", validFields);

  const inputFields = validFields
    .sort((a, b) => a.display_order - b.display_order)
    .map(convertFormFieldToInputField)
    .filter(
      (field): field is TinputField<Record<string, any>> => field !== null,
    );
  // console.log("🚀 ~ inputFields:", inputFields);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)}>
        <div className="flex flex-col gap-5">
          {inputFields.map((fieldConfig) => (
            <FormFieldRenderer
              key={fieldConfig.name?.toString()}
              fieldConfig={fieldConfig}
              form={form}
              isPending={isSubmitting || isDraftSaving}
            />
          ))}
        </div>

        <div className="flex items-center justify-center mb-6 text-sm text-gray-600">
          {currentSectionIndex + 1} of {totalSections} sections
        </div>

        <div className="flex justify-between sm:sticky -bottom-4 bg-[#FBFBFC] pt-9 mb-0">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrevious}
            disabled={isFirstSection || isSubmitting || isDraftSaving}
            className="flex items-center gap-2 min-w-36 min-h-10"
          >
            {t("common.buttonText.previous")}
          </Button>

          {isLastSection ? (
            <Button
              type="submit"
              disabled={isSubmitting || isDraftSaving}
              className="flex items-center gap-2 min-w-36 min-h-10"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  إرسال...
                </>
              ) : (
                t("common.buttonText.submit")
              )}
            </Button>
          ) : (
            <Button
              type="button"
              onClick={handleNext}
              disabled={isSubmitting || isDraftSaving}
              className="flex items-center gap-2 min-w-36 min-h-10"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  إرسال...
                </>
              ) : (
                t("common.buttonText.next")
              )}
            </Button>
          )}
        </div>
      </form>
    </Form>
  );
};
