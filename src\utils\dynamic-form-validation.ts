import { z } from "zod";
import { TFunction } from "@/types";
import { FormField } from "@/types/cm";
import {
  stringValidation,
  optionalStringValidation,
  emailValidation,
  phoneValidation,
  numberValidation,
  booleanValidation,
  timeValidation,
  urlValidation,
  decimalValidation,
  textareaValidation,
  fileValidation,
  arrayValidation,
} from "@/schemas/common-validations";

export const createDynamicFormValidationSchema = (
  fields: FormField[],
  t: TFunction,
) => {
  const schemaFields: Record<string, z.ZodTypeAny> = {};
  console.log(
    "🚀 ~ createDynamicFormValidationSchema ~ schemaFields:",
    schemaFields,
  );

  fields.forEach((field) => {
    const { field_name, field_type, required, data_type } = field;

    if (!field_name) return;

    let fieldSchema: z.ZodTypeAny;

    // Base validation based on field_type and data_type
    switch (field_type) {
      case "text":
      case "input":
        if (data_type === "email_data") {
          fieldSchema = required
            ? emailValidation(t).min(
                1,
                t("common.form.required") || `${field.label} is required`,
              )
            : emailValidation(t).optional();
        } else if (data_type === "phone_data") {
          fieldSchema = required
            ? phoneValidation(t).min(
                1,
                t("common.form.required") || `${field.label} is required`,
              )
            : phoneValidation(t).optional();
        } else if (data_type === "url_data") {
          fieldSchema = required
            ? z
                .string()
                .url(
                  t("common.form.url.error.notValid") || "Invalid URL format",
                )
                .min(
                  1,
                  t("common.form.required") || `${field.label} is required`,
                )
            : urlValidation(t);
        } else {
          fieldSchema = required
            ? stringValidation(t, field.label || field_name)
            : optionalStringValidation();
        }
        break;

      case "textarea":
        const maxLength =
          typeof field.validation_rules?.max_length === "number"
            ? field.validation_rules.max_length
            : 1000;
        fieldSchema = required
          ? stringValidation(t, field.label || field_name).max(maxLength)
          : textareaValidation(t, maxLength);
        break;

      case "number":
        if (data_type === "decimal_data") {
          const decimalPlaces =
            typeof field.validation_rules?.decimal_places === "number"
              ? field.validation_rules.decimal_places
              : 2;
          fieldSchema = required
            ? decimalValidation(t, decimalPlaces)
            : decimalValidation(t, decimalPlaces).optional();
        } else {
          fieldSchema = required
            ? numberValidation(t)
            : numberValidation(t).optional();
        }
        break;

      case "checkbox":
        if (required) {
          fieldSchema = z.coerce.boolean().refine((val) => val === true, {
            message: t("common.form.required") || `${field.label} is required`,
          });
        } else {
          fieldSchema = booleanValidation();
        }
        break;

      case "select":
      case "radio":
        fieldSchema = required
          ? stringValidation(t, field.label || field_name)
          : optionalStringValidation();
        break;

      case "multi_select":
        fieldSchema = required
          ? z
              .array(z.string())
              .min(1, t("common.form.required") || `${field.label} is required`)
          : arrayValidation();
        break;

      case "date":
      case "datetime":
        fieldSchema = required
          ? z.coerce.date({
              required_error:
                t("common.form.required") || `${field.label} is required`,
            })
          : z.coerce.date().optional();
        break;

      case "time":
        fieldSchema = required
          ? timeValidation(t)
          : timeValidation(t).optional();
        break;

      case "file":
        const allowedTypes = Array.isArray(
          field.validation_rules?.allowed_types,
        )
          ? field.validation_rules.allowed_types
          : [];
        fieldSchema = required
          ? fileValidation(t, allowedTypes).refine(
              (file) => file instanceof File,
              t("common.form.required") || `${field.label} is required`,
            )
          : fileValidation(t, allowedTypes);
        break;

      default:
        fieldSchema = required
          ? stringValidation(t, field.label || field_name)
          : optionalStringValidation();
    }

    schemaFields[field_name] = fieldSchema;
  });

  return z.object(schemaFields);
};
