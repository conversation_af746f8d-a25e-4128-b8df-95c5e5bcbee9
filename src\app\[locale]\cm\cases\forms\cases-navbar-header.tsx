"use client";

import { usePathname, useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import ProfileNavHeader from "../../../_modules/people/_components/employees/profile/employee-profile-nav-header";
import { useCase } from "@/app/[locale]/_modules/cm/hooks/useCase";
import { useEffect, useState } from "react";

export default function CasesNavbarHeader() {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const t = useTranslations();
  const id = searchParams.get("case_id") || undefined;
  const [caseId, setCaseId] = useState<string | undefined>(undefined);
  const [caseName, setCaseName] = useState<string | undefined>(undefined);
  const mode = searchParams.get("mode") || "new";
  const { case: caseData, error, isLoading } = useCase(caseId);
  console.log("🚀 ~ CasesNavbarHeader ~ caseData:", caseData);

  useEffect(() => {
    setCaseId(id as string);
    setCaseName(
      mode === "new" && !caseData?.attributes?.beneficiary_name
        ? "New Case"
        : caseData?.attributes?.beneficiary_name || "",
    );
  }, [pathname, id, mode, caseData]);

  return (
    <div className="max-md:px-2">
      <ProfileNavHeader
        headerName={t("common.sidebar.links.cases")}
        name={
          error
            ? `Error: ${error.message || "Failed to load case"}`
            : caseName || ""
        }
        isLoading={isLoading}
      />
    </div>
  );
}
