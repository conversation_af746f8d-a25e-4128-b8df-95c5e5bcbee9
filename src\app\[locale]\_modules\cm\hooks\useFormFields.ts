import { useMemo } from "react";
import useS<PERSON> from "swr";
import { fetcher } from "@/services/fetcher";
import { FormFieldsResponse } from "@/types/cm";

type UseFormFieldsParams = {
  formSectionId?: string | number;
  caseId?: string | number;
};

export const useFormFields = ({
  formSectionId,
  caseId,
}: UseFormFieldsParams = {}) => {
  // Build URL with query parameters
  let apiUrl = "/api/cm/form-fields";
  const params = new URLSearchParams();

  if (formSectionId) {
    params.append("form_section_id", formSectionId.toString());
  }

  if (caseId) {
    params.append("case_id", caseId.toString());
  }

  if (params.toString()) {
    apiUrl += `?${params.toString()}`;
  }

  const { data, error, isLoading, mutate } = useSWR<FormFieldsResponse>(
    // Only fetch if formSectionId is provided
    formSectionId ? apiUrl : null,
    fetcher,
  );
  console.log("🚀 ~ useFormFields ~ data:", data);
  // console.log("🚀 ~ useFormFields ~ data:", data);

  // Handle both response formats:
  // - With case_id: direct format (fields at root level)
  // - Without case_id: JSON:API format (fields under attributes)
  const processedFields = useMemo(() => {
    if (!data?.data) return undefined;

    return data.data
      .map((item) => {
        // Check if this is JSON:API format (has attributes property)
        if ("attributes" in item) {
          return item.attributes;
        }
        // Otherwise, it's direct format
        return item;
      })
      .sort((a, b) => a.display_order - b.display_order);
  }, [data]);

  // console.log("🚀 ~ useFormFields ~ processedFields:", processedFields);

  return {
    fields: processedFields,
    meta: data?.meta,
    isLoading,
    error,
    mutate,
  };
};
