"use client";

import React, { useState, useCallback, useEffect, useRef } from "react";
import { useFormTemplates } from "@/app/[locale]/_modules/cm/hooks/useFormTemplates";
import { useFormSections } from "@/app/[locale]/_modules/cm/hooks/useFormSections";
import { useFormSubmissions } from "@/app/[locale]/_modules/cm/hooks/useFormSubmissions";
import { useCase } from "@/app/[locale]/_modules/cm/hooks/useCase";
import { DynamicForm } from "./dynamic-form";
import { StepTimelineSidebar } from "./step-timeline-sidebar";
import { createFormSubmissionAction } from "@/server/actions/form-submissions";
import { CreateFormSubmissionData } from "@/types/cm";
import { useRouter, usePathname } from "@/i18n/routing";
import { useParams, useSearchParams } from "next/navigation";

type FormsContainerProps = {
  caseId?: string;
  mode: "new" | "edit";
};

export function FormsContainer() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const shouldGoNextRef = useRef(false);

  // Read current values from URL
  const urlCaseId = searchParams.get("case_id");
  const urlMode = searchParams.get("mode") as "new" | "edit";

  const urlTemplateIndex = Number(searchParams.get("templateIndex")) || 0;
  const urlSectionIndex = Number(searchParams.get("sectionIndex")) || 0;

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [completedTemplates, setCompletedTemplates] = useState<Set<number>>(
    new Set(),
  );
  const [completedSections, setCompletedSections] = useState<Set<string>>(
    new Set(),
  );

  const { templates = [], isLoading: templatesLoading } = useFormTemplates({
    sort: "sequence_order",
  });
  const currentTemplate = templates[urlTemplateIndex];
  const currentTemplateId = currentTemplate?.id;

  const { sections = [], isLoading: sectionsLoading } = useFormSections({
    formTemplateId: currentTemplateId,
    sort: "display_order",
  });

  const currentSection = sections[urlSectionIndex];

  const {
    submissions,
    mutate: mutateSubmissions,
    isLoading: submissionsLoading,
  } = useFormSubmissions({
    caseId: urlMode === "edit" ? urlCaseId : undefined,
    formSectionId: currentSection?.id,
  });

  const { isLoading: caseLoading } = useCase(
    urlMode === "edit" ? urlCaseId : undefined,
  );

  const isLoading =
    templatesLoading ||
    sectionsLoading ||
    submissionsLoading ||
    (urlMode === "edit" && urlCaseId && caseLoading);

  const updateSearchParams = useCallback(
    (updates: Record<string, string>) => {
      const params = new URLSearchParams(searchParams);
      Object.entries(updates).forEach(([key, value]) => {
        params.set(key, value);
      });

      const newUrl = `${pathname}?${params.toString()}`;
      console.log("🚀 ~ updateSearchParams ~ newUrl:", newUrl);

      router.replace(newUrl); // 👈 Replace instead of push
    },
    [router, pathname, searchParams],
  );

  const handleNext = () => {
    const nextSectionIndex = urlSectionIndex + 1;
    const nextTemplateIndex = urlTemplateIndex + 1;
    if (nextSectionIndex < sections.length) {
      updateSearchParams({
        templateIndex: urlTemplateIndex.toString(),
        sectionIndex: nextSectionIndex.toString(),
      });
    } else if (nextTemplateIndex < templates.length) {
      updateSearchParams({
        templateIndex: nextTemplateIndex.toString(),
        // sectionIndex: "0",
      });
    }
  };

  const handlePrevious = () => {
    const prevSectionIndex = urlSectionIndex - 1;
    const prevTemplateIndex = urlTemplateIndex - 1;

    if (prevSectionIndex >= 0) {
      updateSearchParams({
        templateIndex: urlTemplateIndex.toString(),
        sectionIndex: prevSectionIndex.toString(),
      });
    } else if (prevTemplateIndex >= 0) {
      const previousTemplate = templates[prevTemplateIndex];
      const sectionCount = previousTemplate?.attributes?.sections_count || 1;

      updateSearchParams({
        templateIndex: prevTemplateIndex.toString(),
        sectionIndex: (sectionCount - 1).toString(),
      });
    }
  };

  const handleSectionClick = (index: number) => {
    updateSearchParams({
      templateIndex: urlTemplateIndex.toString(),
      sectionIndex: index.toString(),
    });
  };

  const handleTemplateChange = (templateIndex: number) => {
    updateSearchParams({
      templateIndex: templateIndex.toString(),
      sectionIndex: "0",
    });
  };

  const handleFormSubmit = async (
    data: Record<string, unknown>,
  ): Promise<{ success: boolean; newCaseId: string | null }> => {
    if (!currentSection?.id) return { success: false, newCaseId: null };

    try {
      const submissionData: CreateFormSubmissionData = {
        form_section_id: currentSection.id,
        form_data: data,
        ...(urlMode === "edit" && urlCaseId && { case_id: urlCaseId }),
        ...(urlMode === "new" && {
          case_type: "standard",
          priority_level: 1,
          confidentiality_level: 1,
        }),
      };

      setIsSubmitting(true);
      const result = await createFormSubmissionAction(
        { success: null, error: null },
        submissionData,
      );

      if (result.success) {
        setCompletedSections((prev) => new Set([...prev, currentSection.id]));

        let nextParams: Record<string, string> = {};

        // Determine next step
        const isLastSectionInTemplate = urlSectionIndex + 1 >= sections.length;
        const nextTemplateIndex =
          urlTemplateIndex < templates.length - 1 && isLastSectionInTemplate
            ? urlTemplateIndex + 1
            : urlTemplateIndex;

        const nextSectionIndex =
          isLastSectionInTemplate && urlTemplateIndex === templates.length - 1
            ? urlSectionIndex
            : isLastSectionInTemplate
              ? 0
              : urlSectionIndex + 1;

        // If we just created a case
        if (urlMode === "new" && result.data?.data?.attributes?.case_id) {
          const newCaseId = result.data.data.attributes.case_id.toString();

          nextParams = {
            case_id: newCaseId,
            mode: "edit",
            templateIndex: nextTemplateIndex.toString(),
            sectionIndex: nextSectionIndex.toString(),
          };
        } else {
          // Normal navigation
          nextParams = {
            case_id: urlCaseId!,
            mode: urlMode,
            templateIndex: nextTemplateIndex.toString(),
            sectionIndex: nextSectionIndex.toString(),
          };
        }

        updateSearchParams(nextParams);
        await mutateSubmissions();

        return { success: true, newCaseId: null };
      } else {
        console.error("Submission failed", result.error);
        return { success: false, newCaseId: null };
      }
    } catch (error) {
      console.error("Submit error:", error);
      return { success: false, newCaseId: null };
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading || !currentTemplate || !currentSection) {
    return <div className="p-4">Loading...</div>;
  }

  return (
    <div className="flex flex-row-reverse gap-8 bg-[hsl(var(--primary),0)]">
      <div className="w-80 flex flex-col gap-8 rounded-xl bg-[#F5F9F4] p-4">
        <StepTimelineSidebar
          caseId={urlCaseId}
          templates={templates}
          currentTemplateIndex={urlTemplateIndex}
          currentSectionIndex={urlSectionIndex}
          completedTemplates={completedTemplates}
          completedSections={completedSections}
          onTemplateClick={handleTemplateChange}
          onSectionClick={handleSectionClick}
        />
      </div>

      <div className="flex-1 flex flex-col">
        <div className="mb-10">
          <div className="flex items-start gap-1 flex-col">
            <h1 className="text-lg text-main-2 font-bold">
              {currentSection.attributes.name}
            </h1>
            <p className="text-sm font-normal text-main-2/60">
              {currentSection.attributes.description ||
                "Complete this section to continue"}
            </p>
          </div>
        </div>

        <DynamicForm
          formSectionId={currentSection.id}
          caseId={urlCaseId}
          shouldGoNextRef={shouldGoNextRef}
          submission={submissions}
          submissionsLoading={submissionsLoading}
          onSubmit={handleFormSubmit}
          onNext={undefined}
          onPrevious={handlePrevious}
          isSubmitting={isSubmitting}
          mode={urlMode === "new" ? "create" : "edit"}
          canGoNext={
            urlSectionIndex < sections.length - 1 ||
            urlTemplateIndex < templates.length - 1
          }
          canGoPrevious={urlSectionIndex > 0 || urlTemplateIndex > 0}
          isFirstSection={urlSectionIndex === 0 && urlTemplateIndex === 0}
          isLastSection={
            urlSectionIndex === sections.length - 1 &&
            urlTemplateIndex === templates.length - 1
          }
          currentSectionIndex={urlSectionIndex}
          totalSections={sections.length}
        />
      </div>
    </div>
  );
}
