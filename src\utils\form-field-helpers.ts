import { Tin<PERSON><PERSON>ield, TFunction } from "@/types";
import { FormInputType } from "@/types/form-types";
import { FormField, FormFieldOption } from "@/types/cm";

export function createBooleanSwitchField<T extends Record<string, unknown>>(
  name: keyof T,
  label: string,
  options: Partial<TinputField<T>> = {},
): TinputField<T> {
  return {
    name,
    type: "switch",
    label,
    ...options,
  };
}

export function createBooleanSelectField<T extends Record<string, unknown>>(
  name: keyof T,
  label: string,
  t: TFunction,
  options: Partial<TinputField<T>> = {},
): TinputField<T> {
  return {
    name,
    type: "select",
    label,
    placeholder: t("common.form.select.placeholder") || "Select an option",
    options: [
      { label: t("common.form.select.yes") || "Yes", value: "true" },
      { label: t("common.form.select.no") || "No", value: "false" },
    ],
    ...options,
  };
}

export function convertBooleanValue(
  value: boolean | string,
  fieldType: "switch" | "select",
): boolean | string {
  if (fieldType === "switch") {
    return value === true || value === "true";
  }
  // For select fields, convert to string
  return String(value === true || value === "true");
}

export function convertToBooleanForAPI(value: unknown): boolean {
  return value === true || value === "true";
}

export function getInputTypeFromAPI(apiType: string): FormInputType {
  switch (apiType.toLowerCase()) {
    case "boolean":
      return "switch";
    case "integer":
    case "float":
    case "number":
    case "decimal":
      return "number";
    case "email":
      return "email";
    case "phone":
    case "mobile":
      return "tel";
    case "time":
      return "time";
    case "date":
      return "date";
    case "datetime":
    case "datetime-local":
      return "datetime-local";
    case "url":
    case "website":
      return "url";
    case "password":
      return "password";
    case "textarea":
    case "longtext":
      return "textarea";
    case "file":
    case "image":
      return "file";
    case "select":
    case "dropdown":
      return "select";
    case "array":
      return "multi-select"; // Arrays will be handled as multi-select
    case "string":
    case "text":
    default:
      return "text";
  }
}

export function convertValueForAPISubmission(
  value: unknown,
  apiType: string,
): unknown {
  switch (apiType.toLowerCase()) {
    case "boolean":
      return value === true || value === "true";
    case "integer":
      return parseInt(String(value), 10);
    case "float":
    case "number":
    case "decimal":
      return parseFloat(String(value));
    case "array":
      if (Array.isArray(value)) {
        return value.map((item) => String(item).padStart(2, "0"));
      }
      return [];
    case "string":
    case "text":
    case "email":
    case "phone":
    case "mobile":
    case "time":
    case "date":
    case "url":
    case "password":
    case "textarea":
    default:
      return String(value || "");
  }
}

// NEW UTILITY FUNCTIONS FOR ENHANCED API RESPONSE FORMAT

/**
 * Maps the new API field_type to frontend FormInputType
 * Uses the existing getInputTypeFromAPI function as a base
 */
export function getInputTypeFromNewAPI(fieldType: string): FormInputType {
  const typeMap: Record<string, FormInputType> = {
    text: "text",
    email: "email",
    password: "password",
    number: "number",
    tel: "tel",
    url: "url",
    textarea: "textarea",
    select_field: "select",
    multiselect: "multi-select",
    checkbox: "checkbox",
    radio: "radio",
    date: "date",
    datetime: "datetime-local",
    time: "time",
    file: "file",
    switch: "switch",
    // Add more mappings as needed
  };

  // Use existing function as fallback for data_type mapping
  return typeMap[fieldType] || getInputTypeFromAPI(fieldType);
}

/**
 * Converts FormFieldOption[] to the format expected by TinputField
 */
export function convertFormFieldOptions(
  options: FormFieldOption[],
): Array<{ value: string; label: string }> {
  return options
    .filter((option) => option.active)
    .sort((a, b) => a.display_order - b.display_order)
    .map((option) => ({
      value: option.option_key,
      label: option.option_value,
    }));
}

/**
 * Converts the new API FormField format to TinputField format
 * This is the main conversion function that uses all the utility functions above
 */
export function convertNewAPIFormFieldToInputField(
  field: FormField,
): TinputField<Record<string, any>> | null {
  // Skip fields that are not enabled or visible
  if (!field.visible) {
    return null;
  }

  // Skip fields with invalid names
  if (
    !field.field_name ||
    typeof field.field_name !== "string" ||
    field.field_name.trim() === ""
  ) {
    console.warn("Skipping field with invalid field_name:", field);
    return null;
  }

  // Convert field type to input type
  const inputType = getInputTypeFromNewAPI(field.field_type);
  console.log(field.options);
  // Convert options if they exist
  const convertedOptions =
    field.options && field.options.length > 0
      ? convertFormFieldOptions(field.options)
      : undefined;

  // Handle boolean fields using existing utility functions
  let finalInputType = inputType;
  if (field.data_type === "boolean_data") {
    // Use switch for boolean fields by default, but allow override
    finalInputType = field.field_type === "select_field" ? "select" : "switch";
  }

  const inputField: TinputField<Record<string, any>> = {
    name: field.field_name as keyof Record<string, any>,
    type: finalInputType,
    label: field.label,
    placeholder: field.placeholder || undefined,
    className: field.required ? "required" : undefined,
    options: convertedOptions,
    // Add help text as description
    ...(field.help_text && { description: field.help_text }),
    // Add any additional field config
    ...(field.field_config &&
      Object.keys(field.field_config).length > 0 && {
        fieldConfig: field.field_config,
      }),
  };

  return inputField;
}

/**
 * Extracts default value from field configuration
 * Checks multiple possible sources for default values
 */
export function extractDefaultValueFromField(field: FormField): unknown {
  // Check for explicit default_value property
  if (field.default_value !== undefined && field.default_value !== null) {
    return field.default_value;
  }

  // Check field_config for default value
  if (field.field_config && typeof field.field_config === "object") {
    const config = field.field_config as Record<string, unknown>;
    if (config.default_value !== undefined && config.default_value !== null) {
      return config.default_value;
    }
    if (config.defaultValue !== undefined && config.defaultValue !== null) {
      return config.defaultValue;
    }
  }

  return null;
}

/**
 * Returns appropriate default values for different field types to prevent
 * uncontrolled component warnings in React
 */
export function getDefaultValueForFieldType(
  fieldType: string,
): string | boolean | number | any[] {
  switch (fieldType) {
    case "text":
    case "email":
    case "password":
    case "textarea":
    case "select":
    case "select_field":
    case "date":
    case "time":
    case "datetime-local":
    case "url":
    case "tel":
      return "";
    case "number":
      return ""; // Use empty string for number inputs to show placeholder
    case "checkbox":
      return false;
    case "switch":
      return false;
    case "radio":
      return "";
    case "multi-select":
      return [];
    default:
      return "";
  }
}

/**
 * Matches a default value to the correct option value for select fields
 * Handles cases where default value might be a label instead of a value
 */
export function matchDefaultValueToOption(
  defaultValue: unknown,
  options: FormFieldOption[],
): string {
  if (!defaultValue || !options || options.length === 0) {
    return "";
  }

  const defaultStr = String(defaultValue);

  // First try exact match with option_key (value)
  const exactValueMatch = options.find((opt) => opt.option_key === defaultStr);
  if (exactValueMatch) {
    return exactValueMatch.option_key;
  }

  // Then try exact match with option_value (label)
  const exactLabelMatch = options.find(
    (opt) => opt.option_value === defaultStr,
  );
  if (exactLabelMatch) {
    return exactLabelMatch.option_key;
  }

  // Try case-insensitive match with option_value (label)
  const caseInsensitiveLabelMatch = options.find(
    (opt) => opt.option_value.toLowerCase() === defaultStr.toLowerCase(),
  );
  if (caseInsensitiveLabelMatch) {
    return caseInsensitiveLabelMatch.option_key;
  }

  // If no match found, return empty string
  return "";
}

/**
 * Gets the final default value for a field, considering both configured defaults
 * and field type defaults
 */
export function getFieldDefaultValue(field: FormField): unknown {
  // First try to get configured default value
  const configuredDefault = extractDefaultValueFromField(field);
  if (configuredDefault !== null) {
    // For select fields, try to match the default value to an option
    if (
      (field.field_type === "select_field" || field.field_type === "select") &&
      field.options
    ) {
      const matchedValue = matchDefaultValueToOption(
        configuredDefault,
        field.options,
      );
      console.log(`🔧 Default value matched for ${field.field_name}:`, {
        configuredDefault,
        matchedValue,
        options: field.options.map((opt) => ({
          key: opt.option_key,
          value: opt.option_value,
        })),
      });
      return matchedValue;
    }
    return configuredDefault;
  }

  // Fall back to field type default
  return getDefaultValueForFieldType(field.field_type);
}
